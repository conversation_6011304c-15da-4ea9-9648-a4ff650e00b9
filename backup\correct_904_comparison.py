import pandas as pd
from datetime import datetime

def compare_904_asset_codes():
    """
    将dig_asset_order_2.xlsx中的904个ASSET_CODE条目与dig_data_asset_2.xlsx对比
    找出未出现的ASSET_CODE并导出到Excel
    """
    print("开始正确的904个ASSET_CODE对比分析...")
    print("=" * 60)
    
    # 1. 读取dig_asset_order_2.xlsx并拆分成904个条目
    print("1. 读取dig_asset_order_2.xlsx并拆分ASSET_CODE...")
    df_order = pd.read_excel('dig_asset_order_2.xlsx')
    
    all_904_entries = []  # 保存所有904个条目
    
    for index, row in df_order.iterrows():
        asset_code = row['ASSET_CODE']
        if not pd.isna(asset_code):
            asset_code_str = str(asset_code).strip()
            if asset_code_str:
                # 按逗号分割
                codes = [code.strip() for code in asset_code_str.split(',')]
                codes = [code for code in codes if code]
                
                # 为每个拆分出的ASSET_CODE创建一个条目
                for code in codes:
                    all_904_entries.append({
                        'entry_id': len(all_904_entries) + 1,  # 给每个条目一个ID
                        'original_row_index': index + 1,
                        'ORDER_ID': row['ORDER_ID'],
                        'BUYER_ID': row['BUYER_ID'],
                        'BUY_QUANTITY': row['BUY_QUANTITY'],
                        'PAY_TIME': row['PAY_TIME'],
                        'original_asset_code': asset_code_str,
                        'split_asset_code': code
                    })
    
    print(f"   拆分后总条目数: {len(all_904_entries)}")
    
    # 2. 读取dig_data_asset_2.xlsx的所有ASSET_CODE
    print("2. 读取dig_data_asset_2.xlsx的ASSET_CODE...")
    df_asset = pd.read_excel('dig_data_asset_2.xlsx')
    
    existing_asset_codes = set()
    for asset_code in df_asset['ASSET_CODE']:
        if not pd.isna(asset_code):
            asset_code_str = str(asset_code).strip()
            if asset_code_str:
                # 处理可能的逗号分隔
                codes = [code.strip() for code in asset_code_str.split(',')]
                codes = [code for code in codes if code]
                existing_asset_codes.update(codes)
    
    print(f"   dig_data_asset_2.xlsx中的ASSET_CODE数量: {len(existing_asset_codes)}")
    
    # 3. 逐一对比904个条目，找出未出现的
    print("3. 逐一对比904个条目...")
    missing_entries = []
    
    for entry in all_904_entries:
        if entry['split_asset_code'] not in existing_asset_codes:
            missing_entries.append(entry)
    
    print(f"   未出现在dig_data_asset_2.xlsx中的条目数: {len(missing_entries)}")
    
    # 4. 创建Excel文件导出结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"missing_from_904_entries_{timestamp}.xlsx"
    
    print(f"4. 创建Excel文件: {excel_filename}")
    
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        
        # 工作表1: 所有904个条目的对比结果
        all_entries_with_status = []
        for entry in all_904_entries:
            entry_copy = entry.copy()
            entry_copy['exists_in_asset_file'] = entry['split_asset_code'] in existing_asset_codes
            entry_copy['status'] = '存在' if entry_copy['exists_in_asset_file'] else '缺失'
            all_entries_with_status.append(entry_copy)
        
        df_all = pd.DataFrame(all_entries_with_status)
        df_all = df_all.sort_values(['entry_id'])
        df_all.to_excel(writer, sheet_name='全部904条目对比结果', index=False)
        print(f"   工作表 '全部904条目对比结果' 创建完成: {len(df_all)} 条记录")
        
        # 工作表2: 仅缺失的条目
        if missing_entries:
            df_missing = pd.DataFrame(missing_entries)
            df_missing = df_missing.sort_values(['entry_id'])
            df_missing.to_excel(writer, sheet_name='缺失的ASSET_CODE条目', index=False)
            print(f"   工作表 '缺失的ASSET_CODE条目' 创建完成: {len(df_missing)} 条记录")
        
        # 工作表3: 缺失ASSET_CODE的唯一列表
        if missing_entries:
            unique_missing_codes = sorted(set(entry['split_asset_code'] for entry in missing_entries))
            
            unique_missing_data = []
            for code in unique_missing_codes:
                # 统计这个ASSET_CODE在904个条目中出现的次数
                occurrences = [entry for entry in missing_entries if entry['split_asset_code'] == code]
                
                unique_missing_data.append({
                    'MISSING_ASSET_CODE': code,
                    'OCCURRENCE_COUNT': len(occurrences),
                    'FIRST_ORDER_ID': occurrences[0]['ORDER_ID'],
                    'FIRST_BUYER_ID': occurrences[0]['BUYER_ID'],
                    'ALL_ORDER_IDS': ', '.join(str(entry['ORDER_ID']) for entry in occurrences),
                    'ALL_BUYER_IDS': ', '.join(str(entry['BUYER_ID']) for entry in occurrences)
                })
            
            df_unique_missing = pd.DataFrame(unique_missing_data)
            df_unique_missing.to_excel(writer, sheet_name='缺失ASSET_CODE唯一列表', index=False)
            print(f"   工作表 '缺失ASSET_CODE唯一列表' 创建完成: {len(df_unique_missing)} 个唯一ASSET_CODE")
        
        # 工作表4: 统计汇总
        stats_data = [
            ['对比统计', ''],
            ['dig_asset_order_2.xlsx原始行数', len(df_order)],
            ['拆分后总条目数', len(all_904_entries)],
            ['dig_data_asset_2.xlsx ASSET_CODE数量', len(existing_asset_codes)],
            ['', ''],
            ['对比结果', ''],
            ['存在的条目数', len(all_904_entries) - len(missing_entries)],
            ['缺失的条目数', len(missing_entries)],
            ['缺失的唯一ASSET_CODE数量', len(set(entry['split_asset_code'] for entry in missing_entries)) if missing_entries else 0],
            ['', ''],
            ['缺失率', ''],
            ['条目缺失率', f"{len(missing_entries) / len(all_904_entries) * 100:.2f}%" if all_904_entries else "0%"],
            ['唯一ASSET_CODE缺失率', f"{len(set(entry['split_asset_code'] for entry in missing_entries)) / len(set(entry['split_asset_code'] for entry in all_904_entries)) * 100:.2f}%" if all_904_entries else "0%"]
        ]
        
        df_stats = pd.DataFrame(stats_data, columns=['统计项目', '数值'])
        df_stats.to_excel(writer, sheet_name='统计汇总', index=False)
        print(f"   工作表 '统计汇总' 创建完成")
    
    print(f"\n✅ Excel文件创建成功: {excel_filename}")
    print(f"\n📊 最终统计:")
    print(f"总条目数: {len(all_904_entries)}")
    print(f"缺失条目数: {len(missing_entries)}")
    print(f"缺失唯一ASSET_CODE数量: {len(set(entry['split_asset_code'] for entry in missing_entries)) if missing_entries else 0}")
    print(f"条目缺失率: {len(missing_entries) / len(all_904_entries) * 100:.2f}%" if all_904_entries else "0%")
    
    # 显示前10个缺失的ASSET_CODE
    if missing_entries:
        unique_missing = sorted(set(entry['split_asset_code'] for entry in missing_entries))
        print(f"\n前10个缺失的ASSET_CODE:")
        for i, code in enumerate(unique_missing[:10], 1):
            count = len([entry for entry in missing_entries if entry['split_asset_code'] == code])
            print(f"  {i:2d}. {code} (在904个条目中出现{count}次)")
    
    return excel_filename, len(missing_entries), len(set(entry['split_asset_code'] for entry in missing_entries)) if missing_entries else 0

if __name__ == "__main__":
    excel_file, missing_count, unique_missing_count = compare_904_asset_codes()
