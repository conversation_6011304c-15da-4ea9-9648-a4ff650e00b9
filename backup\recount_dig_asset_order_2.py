import pandas as pd

def detailed_count_asset_codes():
    """
    详细统计dig_asset_order_2.xlsx中的ASSET_CODE，确保正确处理逗号分隔
    """
    filename = 'dig_asset_order_2.xlsx'
    print(f"重新详细统计文件: {filename}")
    print("=" * 60)
    
    try:
        # 读取Excel文件
        df = pd.read_excel(filename)
        print(f"文件读取成功")
        print(f"总行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        if 'ASSET_CODE' not in df.columns:
            print("错误: 文件中没有找到ASSET_CODE列")
            return
        
        # 详细分析每一行
        all_asset_codes = set()
        row_details = []
        total_original_entries = 0
        total_after_split = 0
        comma_separated_count = 0
        
        print(f"\n开始逐行分析ASSET_CODE...")
        
        for index, row in df.iterrows():
            asset_code = row['ASSET_CODE']
            if not pd.isna(asset_code):
                total_original_entries += 1
                
                # 转换为字符串并处理
                asset_code_str = str(asset_code).strip()
                if asset_code_str:
                    # 按逗号分割
                    codes = [code.strip() for code in asset_code_str.split(',')]
                    codes = [code for code in codes if code]  # 过滤空字符串
                    
                    # 添加到总集合
                    all_asset_codes.update(codes)
                    total_after_split += len(codes)
                    
                    # 记录详细信息
                    row_detail = {
                        'row_index': index + 1,
                        'original_asset_code': asset_code_str,
                        'split_count': len(codes),
                        'split_codes': codes,
                        'is_comma_separated': len(codes) > 1
                    }
                    row_details.append(row_detail)
                    
                    if len(codes) > 1:
                        comma_separated_count += 1
        
        print(f"\n📊 统计结果:")
        print(f"包含ASSET_CODE的原始行数: {total_original_entries}")
        print(f"包含逗号分隔的行数: {comma_separated_count}")
        print(f"拆分前的ASSET_CODE条目数: {total_original_entries}")
        print(f"拆分后的ASSET_CODE条目数: {total_after_split}")
        print(f"唯一ASSET_CODE总数: {len(all_asset_codes)}")
        print(f"通过逗号分隔增加的条目数: {total_after_split - total_original_entries}")
        
        # 分析ASSET_CODE的数字范围
        numbers = []
        for code in all_asset_codes:
            if '#' in code:
                try:
                    num = int(code.split('#')[-1])
                    numbers.append(num)
                except:
                    pass
        
        if numbers:
            print(f"ASSET_CODE数字范围: {min(numbers)} - {max(numbers)}")
            print(f"数字范围跨度: {max(numbers) - min(numbers) + 1}")
        
        # 显示逗号分隔的详细例子
        comma_separated_rows = [detail for detail in row_details if detail['is_comma_separated']]
        if comma_separated_rows:
            print(f"\n🔍 逗号分隔的详细分析:")
            print(f"逗号分隔行数: {len(comma_separated_rows)}")
            
            # 统计每行分隔出的数量
            split_counts = [detail['split_count'] for detail in comma_separated_rows]
            print(f"每行分隔数量统计:")
            print(f"  最少分隔数: {min(split_counts)}")
            print(f"  最多分隔数: {max(split_counts)}")
            print(f"  平均分隔数: {sum(split_counts) / len(split_counts):.1f}")
            
            print(f"\n前10个逗号分隔的例子:")
            for i, detail in enumerate(comma_separated_rows[:10], 1):
                print(f"  {i:2d}. 行{detail['row_index']:3d}: 拆分为{detail['split_count']:2d}个")
                print(f"      原始: {detail['original_asset_code']}")
                print(f"      拆分: {detail['split_codes']}")
                print()
        
        # 显示前20个唯一的ASSET_CODE
        print(f"\n📋 前20个唯一ASSET_CODE:")
        sorted_codes = sorted(all_asset_codes)
        for i, code in enumerate(sorted_codes[:20], 1):
            print(f"  {i:2d}. {code}")
        
        if len(sorted_codes) > 20:
            print(f"  ... 还有 {len(sorted_codes) - 20} 个")
        
        # 保存详细结果
        print(f"\n💾 保存详细分析结果...")
        
        with open('dig_asset_order_2_detailed_count.txt', 'w', encoding='utf-8') as f:
            f.write("dig_asset_order_2.xlsx ASSET_CODE详细统计\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"包含ASSET_CODE的原始行数: {total_original_entries}\n")
            f.write(f"包含逗号分隔的行数: {comma_separated_count}\n")
            f.write(f"拆分后的ASSET_CODE条目数: {total_after_split}\n")
            f.write(f"唯一ASSET_CODE总数: {len(all_asset_codes)}\n")
            if numbers:
                f.write(f"ASSET_CODE数字范围: {min(numbers)} - {max(numbers)}\n")
            f.write(f"\n所有唯一ASSET_CODE列表:\n")
            f.write("-" * 30 + "\n")
            for i, code in enumerate(sorted_codes, 1):
                f.write(f"{i:3d}. {code}\n")
        
        print("详细结果已保存到: dig_asset_order_2_detailed_count.txt")
        
        return {
            'total_rows': len(df),
            'original_entries': total_original_entries,
            'comma_separated_rows': comma_separated_count,
            'total_after_split': total_after_split,
            'unique_asset_codes': len(all_asset_codes),
            'asset_codes': all_asset_codes,
            'number_range': (min(numbers), max(numbers)) if numbers else None
        }
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

if __name__ == "__main__":
    result = detailed_count_asset_codes()
    
    if result:
        print(f"\n🎯 最终统计结果:")
        print(f"文件总行数: {result['total_rows']}")
        print(f"原始ASSET_CODE条目数: {result['original_entries']}")
        print(f"逗号分隔行数: {result['comma_separated_rows']}")
        print(f"拆分后总条目数: {result['total_after_split']}")
        print(f"唯一ASSET_CODE数量: {result['unique_asset_codes']}")
        if result['number_range']:
            print(f"数字范围: {result['number_range'][0]} - {result['number_range'][1]}")
