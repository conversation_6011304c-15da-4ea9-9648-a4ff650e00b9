#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理 dig_asset_order.xlsx 文件
将 Sheet1 的数据匹配到 Sheet2 中的指定列
"""

import pandas as pd
import sys
from datetime import datetime

def process_excel_data(file_path):
    """
    处理 Excel 数据的主函数
    
    处理规则：
    1. 数据从 dig_asset_order.xlsx 的第一行开始处理直到 Sheet2 中的所有行全部处理完成
    2. 用 dig_asset_order.xlsx 中 Sheet2 的 ASSET_CODE 去匹配 Sheet1 中的 ASSET_CODE，找到匹配的行
    3. 将 Sheet1 匹配行的 BUYER_ID 写入到 Sheet2 的 USER_ID
    4. 将 Sheet1 匹配行的 ORDER_ID 写入到 Sheet2 的 ACQUIRE_RECORD_ID
    5. 将 Sheet1 匹配行的 PAY_TIME 写入到 Sheet2 的 STATUS_DATE、CREATE_DATE 和 UPDATE_DATE
    """
    
    try:
        print(f"正在读取文件: {file_path}")
        
        # 读取 Excel 文件的两个工作表
        sheet1_df = pd.read_excel(file_path, sheet_name='Sheet1')
        sheet2_df = pd.read_excel(file_path, sheet_name='Sheet2')
        
        print(f"Sheet1 行数: {len(sheet1_df)}")
        print(f"Sheet2 行数: {len(sheet2_df)}")
        
        # 显示 Sheet1 的列名
        print(f"\nSheet1 列名: {list(sheet1_df.columns)}")
        print(f"Sheet2 列名: {list(sheet2_df.columns)}")
        
        # 检查必要的列是否存在
        required_sheet1_cols = ['ASSET_CODE', 'BUYER_ID', 'ORDER_ID', 'PAY_TIME']
        required_sheet2_cols = ['ASSET_CODE', 'USER_ID', 'ACQUIRE_RECORD_ID', 'STATUS_DATE', 'CREATE_DATE', 'UPDATE_DATE']
        
        missing_sheet1_cols = [col for col in required_sheet1_cols if col not in sheet1_df.columns]
        missing_sheet2_cols = [col for col in required_sheet2_cols if col not in sheet2_df.columns]
        
        if missing_sheet1_cols:
            print(f"错误: Sheet1 缺少必要的列: {missing_sheet1_cols}")
            return False
            
        if missing_sheet2_cols:
            print(f"错误: Sheet2 缺少必要的列: {missing_sheet2_cols}")
            return False
        
        # 创建 Sheet1 的 ASSET_CODE 到其他字段的映射字典
        sheet1_mapping = {}
        for _, row in sheet1_df.iterrows():
            asset_code = row['ASSET_CODE']
            if pd.notna(asset_code):  # 确保 ASSET_CODE 不为空
                sheet1_mapping[asset_code] = {
                    'BUYER_ID': row['BUYER_ID'],
                    'ORDER_ID': row['ORDER_ID'],
                    'PAY_TIME': row['PAY_TIME']
                }
        
        print(f"\nSheet1 中有效的 ASSET_CODE 映射数量: {len(sheet1_mapping)}")
        
        # 处理 Sheet2 的数据
        matched_count = 0
        unmatched_count = 0
        
        for index, row in sheet2_df.iterrows():
            asset_code = row['ASSET_CODE']
            
            if pd.notna(asset_code) and asset_code in sheet1_mapping:
                # 找到匹配的数据
                mapping_data = sheet1_mapping[asset_code]
                
                # 更新 Sheet2 的对应列
                sheet2_df.at[index, 'USER_ID'] = mapping_data['BUYER_ID']
                sheet2_df.at[index, 'ACQUIRE_RECORD_ID'] = mapping_data['ORDER_ID']
                sheet2_df.at[index, 'STATUS_DATE'] = mapping_data['PAY_TIME']
                sheet2_df.at[index, 'CREATE_DATE'] = mapping_data['PAY_TIME']
                sheet2_df.at[index, 'UPDATE_DATE'] = mapping_data['PAY_TIME']
                
                matched_count += 1
            else:
                unmatched_count += 1
                if pd.notna(asset_code):
                    print(f"警告: 在 Sheet1 中未找到 ASSET_CODE: {asset_code}")
        
        print(f"\n处理结果:")
        print(f"匹配成功的行数: {matched_count}")
        print(f"未匹配的行数: {unmatched_count}")
        
        # 保存更新后的文件
        output_file = file_path.replace('.xlsx', '_processed.xlsx')
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            sheet1_df.to_excel(writer, sheet_name='Sheet1', index=False)
            sheet2_df.to_excel(writer, sheet_name='Sheet2', index=False)
        
        print(f"\n处理完成！结果已保存到: {output_file}")
        
        # 显示前几行处理后的数据作为验证
        print(f"\nSheet2 处理后的前5行数据:")
        print(sheet2_df[['ASSET_CODE', 'USER_ID', 'ACQUIRE_RECORD_ID', 'STATUS_DATE', 'CREATE_DATE', 'UPDATE_DATE']].head())
        
        return True
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    file_path = 'dig_asset_order.xlsx'
    
    print("开始处理 Excel 数据...")
    print("=" * 50)
    
    success = process_excel_data(file_path)
    
    if success:
        print("=" * 50)
        print("数据处理成功完成！")
    else:
        print("=" * 50)
        print("数据处理失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
