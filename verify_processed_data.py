#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证处理后的 Excel 数据
"""

import pandas as pd

def verify_processed_data():
    """验证处理后的数据"""
    
    try:
        # 读取处理后的文件
        sheet1_df = pd.read_excel('dig_asset_order_processed.xlsx', sheet_name='Sheet1')
        sheet2_df = pd.read_excel('dig_asset_order_processed.xlsx', sheet_name='Sheet2')
        
        print("验证处理后的数据...")
        print("=" * 50)
        
        # 统计 Sheet2 中已填充的数据
        user_id_filled = sheet2_df['USER_ID'].notna().sum()
        acquire_record_id_filled = sheet2_df['ACQUIRE_RECORD_ID'].notna().sum()
        status_date_filled = sheet2_df['STATUS_DATE'].notna().sum()
        create_date_filled = sheet2_df['CREATE_DATE'].notna().sum()
        update_date_filled = sheet2_df['UPDATE_DATE'].notna().sum()
        
        total_rows = len(sheet2_df)
        
        print(f"Sheet2 总行数: {total_rows}")
        print(f"USER_ID 已填充: {user_id_filled} ({user_id_filled/total_rows*100:.1f}%)")
        print(f"ACQUIRE_RECORD_ID 已填充: {acquire_record_id_filled} ({acquire_record_id_filled/total_rows*100:.1f}%)")
        print(f"STATUS_DATE 已填充: {status_date_filled} ({status_date_filled/total_rows*100:.1f}%)")
        print(f"CREATE_DATE 已填充: {create_date_filled} ({create_date_filled/total_rows*100:.1f}%)")
        print(f"UPDATE_DATE 已填充: {update_date_filled} ({update_date_filled/total_rows*100:.1f}%)")
        
        # 显示一些成功匹配的示例
        print(f"\n成功匹配的示例数据:")
        print("-" * 50)
        matched_data = sheet2_df[sheet2_df['USER_ID'].notna()].head(10)
        print(matched_data[['ASSET_CODE', 'USER_ID', 'ACQUIRE_RECORD_ID', 'STATUS_DATE', 'CREATE_DATE', 'UPDATE_DATE']])
        
        # 显示一些未匹配的示例
        print(f"\n未匹配的示例数据:")
        print("-" * 50)
        unmatched_data = sheet2_df[sheet2_df['USER_ID'].isna()].head(10)
        print(unmatched_data[['ASSET_CODE', 'USER_ID', 'ACQUIRE_RECORD_ID', 'STATUS_DATE', 'CREATE_DATE', 'UPDATE_DATE']])
        
        # 检查数据一致性
        print(f"\n数据一致性检查:")
        print("-" * 50)
        
        # 检查填充的数据是否一致
        filled_rows = sheet2_df[sheet2_df['USER_ID'].notna()]
        if len(filled_rows) > 0:
            # 检查同一行的三个日期字段是否一致
            date_consistency = (
                (filled_rows['STATUS_DATE'] == filled_rows['CREATE_DATE']) & 
                (filled_rows['CREATE_DATE'] == filled_rows['UPDATE_DATE'])
            ).all()
            
            print(f"日期字段一致性: {'通过' if date_consistency else '失败'}")
            
            # 检查是否有空值
            user_id_nulls = filled_rows['USER_ID'].isna().sum()
            acquire_record_id_nulls = filled_rows['ACQUIRE_RECORD_ID'].isna().sum()
            
            print(f"已匹配行中的空值检查:")
            print(f"  USER_ID 空值: {user_id_nulls}")
            print(f"  ACQUIRE_RECORD_ID 空值: {acquire_record_id_nulls}")
        
        print("=" * 50)
        print("验证完成！")
        
    except Exception as e:
        print(f"验证过程中发生错误: {str(e)}")

if __name__ == "__main__":
    verify_processed_data()
