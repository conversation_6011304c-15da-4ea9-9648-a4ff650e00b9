-- =====================================================
-- 支付宝账务数据批量插入脚本
-- 表名: dig_asset_pay_order
-- 数据来源: 2088260786994890-20250801-168426223-账务组合查询-simple.xls
-- 总记录数: 3177条
-- 生成时间: 2025-08-01
-- =====================================================

-- 设置会话参数以优化批量插入性能
SET autocommit = 0;
SET unique_checks = 0;
SET foreign_key_checks = 0;

-- 开始事务
START TRANSACTION;

-- 清空表（如果需要重新导入）
-- TRUNCATE TABLE dig_asset_pay_order;

-- 批量插入数据（前50条示例）
INSERT INTO dig_asset_pay_order (
    serial_number, entry_time, alipay_trade_no, alipay_serial_no, merchant_order_no,
    account_type, income_amount, expense_amount, account_balance, service_fee,
    payment_channel, contract_product, counterpart_account, counterpart_name,
    bank_order_no, product_name, remark, business_base_order_no, business_order_no,
    business_bill_source, business_description, payment_remark
) VALUES 
(1, '2025-07-31 21:35:54', '2025073122001480901401230459', '1470667033490724900', 'DIG_103020250731FBF08',
 '收费', 0.00, 4.19, 3935.20, 0.00,
 '支付宝账号', '即时到帐', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '', '敬请期待', '支付宝服务费[2025073122001480901401230459]', 'DIG_103020250731FBF08', 'M{DIG_103020250731FBF08}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(2, '2025-07-31 21:35:53', '2025073122001480901401230459', '1470665280583680901', 'DIG_103020250731FBF08',
 '在线支付', 699.00, 0.00, 3939.39, 4.19,
 '支付宝账号', '即时到帐', '198******45', '**程',
 '', '敬请期待', '', '', '',
 '', '', ''),

(3, '2025-07-31 20:40:25', '2025073122001427601420980248', '1470663375081836600', 'DIG_12226202507319FCC7',
 '收费', 0.00, 1.26, 3240.39, 0.00,
 '快捷支付-信用卡', '即时到帐', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '2025073170283516030560090211302', '敬请期待', '服务费[2025073122001427601420980248]', 'DIG_12226202507319FCC7', 'M{DIG_12226202507319FCC7}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(4, '2025-07-31 20:40:24', '2025073122001427601420980248', '1470665289985427601', 'DIG_12226202507319FCC7',
 '在线支付', 209.70, 0.00, 3241.65, 1.26,
 '快捷支付-信用卡', '即时到帐', '139******94', '*立',
 '2025073170283516030560090211302', '敬请期待', '', '', '',
 '', '', ''),

(5, '2025-07-31 19:49:40', '2025073122001410771404347104', '1470655205597169770', 'DIG_6396220250731C9C17',
 '收费', 0.00, 0.42, 3031.95, 0.00,
 '快捷支付-借记卡', '即时到帐', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '2025073162848989250577090200607', '敬请期待', '服务费[2025073122001410771404347104]', 'DIG_6396220250731C9C17', 'M{DIG_6396220250731C9C17}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(6, '2025-07-31 19:49:40', '2025073122001410771404347104', '1470659133758210771', 'DIG_6396220250731C9C17',
 '在线支付', 69.90, 0.00, 3032.37, 0.42,
 '快捷支付-借记卡', '即时到帐', '155******87', '**龙',
 '2025073162848989250577090200607', '敬请期待', '', '', '',
 '', '', ''),

(7, '2025-07-31 19:24:59', '2025073123001495271452648820', '1470654546004593270', 'DIG_103672202507314B4A9',
 '收费', 0.00, 0.01, 2962.47, 0.00,
 '快捷支付-借记卡', '即时到帐', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '0731a67525552720', '数资萌使·凌宝', '服务费[2025073123001495271452648820]', 'DIG_103672202507314B4A9', 'M{DIG_103672202507314B4A9}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(8, '2025-07-31 19:24:58', '2025073123001495271452648820', '1470657435737795271', 'DIG_103672202507314B4A9',
 '在线支付', 1.00, 0.00, 2962.48, 0.01,
 '快捷支付-借记卡', '即时到帐', '176***@qq.com', '*龙',
 '0731a67525552720', '数资萌使·凌宝', '', '', '',
 '', '', ''),

(9, '2025-07-31 19:24:35', '2025073122001456181431170946', '1470687738401320180', 'DIG_10364920250731B04C0',
 '收费', 0.00, 0.01, 2961.48, 0.00,
 '快捷支付-借记卡', '', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '2025073162602595660518090111408', '数资萌使·凌宝', '服务费[2025073122001456181431170946]', 'DIG_10364920250731B04C0', 'M{DIG_10364920250731B04C0}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(10, '2025-07-31 19:24:35', '2025073122001456181431170946', '1470692381733056181', 'DIG_10364920250731B04C0',
 '在线支付', 1.00, 0.00, 2961.49, 0.01,
 '快捷支付-借记卡', '即时到帐', '392***@qq.com', '*坡',
 '2025073162602595660518090111408', '数资萌使·凌宝', '', '', '',
 '', '', '');

-- 提交事务
COMMIT;

-- 恢复会话参数
SET unique_checks = 1;
SET foreign_key_checks = 1;
SET autocommit = 1;

-- 验证插入结果
SELECT 
    COUNT(*) AS '总记录数',
    MIN(entry_time) AS '最早时间',
    MAX(entry_time) AS '最晚时间',
    SUM(CASE WHEN account_type = '在线支付' THEN income_amount ELSE 0 END) AS '总收入',
    SUM(CASE WHEN account_type = '收费' THEN expense_amount ELSE 0 END) AS '总支出',
    SUM(service_fee) AS '总服务费'
FROM dig_asset_pay_order;

-- 按账务类型统计
SELECT 
    account_type AS '账务类型',
    COUNT(*) AS '交易笔数',
    SUM(income_amount) AS '收入金额',
    SUM(expense_amount) AS '支出金额',
    AVG(account_balance) AS '平均余额'
FROM dig_asset_pay_order 
GROUP BY account_type;

-- 按支付渠道统计（在线支付）
SELECT 
    payment_channel AS '支付渠道',
    COUNT(*) AS '交易笔数',
    SUM(income_amount) AS '收入金额',
    AVG(income_amount) AS '平均金额'
FROM dig_asset_pay_order 
WHERE account_type = '在线支付' AND income_amount > 0
GROUP BY payment_channel
ORDER BY SUM(income_amount) DESC;

-- 说明：
-- 1. 此文件仅包含前10条示例数据
-- 2. 完整的3177条数据请使用 insert_statements.sql 文件
-- 3. 建议分批执行大量数据插入，每批1000条记录
-- 4. 执行前请确保表结构已正确创建
