import pandas as pd
from datetime import datetime

def find_310_differences():
    """
    基于拆分后的904个ASSET_CODE条目找出与dig_data_asset_2.xlsx的差异
    """
    print("开始分析310个差异ASSET_CODE...")
    print("=" * 60)
    
    # 1. 读取dig_asset_order_2.xlsx并拆分所有ASSET_CODE
    print("1. 读取dig_asset_order_2.xlsx并拆分ASSET_CODE...")
    df_order = pd.read_excel('dig_asset_order_2.xlsx')
    
    all_order_entries = []  # 保存所有拆分后的条目（包括重复）
    
    for index, row in df_order.iterrows():
        asset_code = row['ASSET_CODE']
        if not pd.isna(asset_code):
            asset_code_str = str(asset_code).strip()
            if asset_code_str:
                # 按逗号分割
                codes = [code.strip() for code in asset_code_str.split(',')]
                codes = [code for code in codes if code]
                
                # 为每个拆分出的ASSET_CODE创建一个条目
                for code in codes:
                    all_order_entries.append({
                        'original_row_index': index + 1,
                        'ORDER_ID': row['ORDER_ID'],
                        'BUYER_ID': row['BUYER_ID'],
                        'BUY_QUANTITY': row['BUY_QUANTITY'],
                        'PAY_TIME': row['PAY_TIME'],
                        'original_asset_code': asset_code_str,
                        'split_asset_code': code
                    })
    
    print(f"   拆分后总条目数: {len(all_order_entries)}")
    
    # 2. 读取dig_data_asset_2.xlsx的ASSET_CODE
    print("2. 读取dig_data_asset_2.xlsx的ASSET_CODE...")
    df_asset = pd.read_excel('dig_data_asset_2.xlsx')
    
    existing_asset_codes = set()
    for asset_code in df_asset['ASSET_CODE']:
        if not pd.isna(asset_code):
            asset_code_str = str(asset_code).strip()
            if asset_code_str:
                # 也处理可能的逗号分隔
                codes = [code.strip() for code in asset_code_str.split(',')]
                codes = [code for code in codes if code]
                existing_asset_codes.update(codes)
    
    print(f"   dig_data_asset_2.xlsx中的ASSET_CODE数量: {len(existing_asset_codes)}")
    
    # 3. 找出差异条目
    print("3. 查找差异条目...")
    missing_entries = []
    
    for entry in all_order_entries:
        if entry['split_asset_code'] not in existing_asset_codes:
            missing_entries.append(entry)
    
    print(f"   找到差异条目数: {len(missing_entries)}")
    
    # 4. 创建Excel文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"difference_310_asset_codes_{timestamp}.xlsx"
    
    print(f"4. 创建Excel文件: {excel_filename}")
    
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        
        # 工作表1: 所有差异条目详细信息
        df_missing = pd.DataFrame(missing_entries)
        df_missing = df_missing.sort_values(['original_row_index', 'split_asset_code'])
        df_missing.to_excel(writer, sheet_name='差异条目详细信息', index=False)
        print(f"   工作表 '差异条目详细信息' 创建完成: {len(df_missing)} 条记录")
        
        # 工作表2: 差异ASSET_CODE汇总
        missing_codes = [entry['split_asset_code'] for entry in missing_entries]
        unique_missing_codes = sorted(set(missing_codes))
        
        # 统计每个ASSET_CODE的出现次数
        code_counts = {}
        for code in missing_codes:
            code_counts[code] = code_counts.get(code, 0) + 1
        
        summary_data = []
        for code in unique_missing_codes:
            # 找到包含这个ASSET_CODE的所有条目
            related_entries = [entry for entry in missing_entries if entry['split_asset_code'] == code]
            
            summary_data.append({
                'MISSING_ASSET_CODE': code,
                'OCCURRENCE_COUNT': code_counts[code],
                'ORDER_IDS': ', '.join(str(entry['ORDER_ID']) for entry in related_entries),
                'BUYER_IDS': ', '.join(str(entry['BUYER_ID']) for entry in related_entries),
                'TOTAL_QUANTITY': sum(entry['BUY_QUANTITY'] for entry in related_entries),
                'PAY_TIMES': ', '.join(str(entry['PAY_TIME']) for entry in related_entries)
            })
        
        df_summary = pd.DataFrame(summary_data)
        df_summary.to_excel(writer, sheet_name='差异ASSET_CODE汇总', index=False)
        print(f"   工作表 '差异ASSET_CODE汇总' 创建完成: {len(df_summary)} 个唯一ASSET_CODE")
        
        # 工作表3: 按原始行分组
        row_groups = {}
        for entry in missing_entries:
            row_key = entry['original_row_index']
            if row_key not in row_groups:
                row_groups[row_key] = []
            row_groups[row_key].append(entry)
        
        row_summary_data = []
        for row_index, entries in sorted(row_groups.items()):
            first_entry = entries[0]
            missing_codes_in_row = [entry['split_asset_code'] for entry in entries]
            
            row_summary_data.append({
                'ORIGINAL_ROW_INDEX': row_index,
                'ORDER_ID': first_entry['ORDER_ID'],
                'BUYER_ID': first_entry['BUYER_ID'],
                'BUY_QUANTITY': first_entry['BUY_QUANTITY'],
                'PAY_TIME': first_entry['PAY_TIME'],
                'ORIGINAL_ASSET_CODE': first_entry['original_asset_code'],
                'MISSING_COUNT_IN_ROW': len(missing_codes_in_row),
                'MISSING_ASSET_CODES': ', '.join(missing_codes_in_row)
            })
        
        df_row_summary = pd.DataFrame(row_summary_data)
        df_row_summary.to_excel(writer, sheet_name='按原始行分组', index=False)
        print(f"   工作表 '按原始行分组' 创建完成: {len(df_row_summary)} 行")
        
        # 工作表4: 统计分析
        stats_data = [
            ['总体统计', ''],
            ['dig_asset_order_2.xlsx原始行数', len(df_order)],
            ['拆分后总条目数', len(all_order_entries)],
            ['dig_data_asset_2.xlsx ASSET_CODE数量', len(existing_asset_codes)],
            ['差异条目总数', len(missing_entries)],
            ['差异唯一ASSET_CODE数量', len(unique_missing_codes)],
            ['', ''],
            ['差异分析', ''],
            ['最多重复的ASSET_CODE', max(code_counts.values()) if code_counts else 0],
            ['平均每个ASSET_CODE重复次数', sum(code_counts.values()) / len(code_counts) if code_counts else 0],
            ['受影响的原始行数', len(row_groups)],
            ['平均每行缺失ASSET_CODE数', len(missing_entries) / len(row_groups) if row_groups else 0]
        ]
        
        df_stats = pd.DataFrame(stats_data, columns=['统计项目', '数值'])
        df_stats.to_excel(writer, sheet_name='统计分析', index=False)
        print(f"   工作表 '统计分析' 创建完成")
    
    print(f"\n✅ Excel文件创建成功: {excel_filename}")
    print(f"\n📊 最终统计:")
    print(f"差异条目总数: {len(missing_entries)}")
    print(f"差异唯一ASSET_CODE数量: {len(unique_missing_codes)}")
    print(f"受影响的原始行数: {len(row_groups)}")
    
    # 显示前10个差异ASSET_CODE
    print(f"\n前10个差异ASSET_CODE:")
    for i, code in enumerate(unique_missing_codes[:10], 1):
        count = code_counts[code]
        print(f"  {i:2d}. {code} (出现{count}次)")
    
    return excel_filename, len(missing_entries), len(unique_missing_codes)

if __name__ == "__main__":
    excel_file, total_entries, unique_codes = find_310_differences()
