import pandas as pd
from datetime import datetime

def create_missing_asset_excel():
    """
    将缺失的ASSET_CODE数据创建为新的Excel文件
    """
    print("开始创建缺失ASSET_CODE的Excel文件...")
    
    # 读取详细的CSV文件
    try:
        df_missing = pd.read_csv('missing_asset_codes_details.csv', encoding='utf-8-sig')
        print(f"读取到 {len(df_missing)} 条缺失ASSET_CODE记录")
    except Exception as e:
        print(f"读取missing_asset_codes_details.csv失败: {e}")
        return
    
    # 读取原始的dig_asset_order_2.xlsx以获取完整的行信息
    try:
        df_order = pd.read_excel('dig_asset_order_2.xlsx')
        print(f"读取原始订单数据: {len(df_order)} 行")
    except Exception as e:
        print(f"读取dig_asset_order_2.xlsx失败: {e}")
        return
    
    # 创建多个工作表的Excel文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"missing_asset_codes_{timestamp}.xlsx"
    
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        
        # 工作表1: 缺失ASSET_CODE汇总
        missing_summary = df_missing.groupby('missing_asset_code').agg({
            'row_index': 'count',
            'ORDER_ID': lambda x: ', '.join(map(str, x.unique())),
            'BUYER_ID': lambda x: ', '.join(map(str, x.unique())),
            'BUY_QUANTITY': 'sum',
            'PAY_TIME': lambda x: ', '.join(x.unique())
        }).reset_index()
        
        missing_summary.columns = [
            'MISSING_ASSET_CODE', 
            'OCCURRENCE_COUNT', 
            'ORDER_IDS', 
            'BUYER_IDS', 
            'TOTAL_QUANTITY', 
            'PAY_TIMES'
        ]
        
        missing_summary = missing_summary.sort_values('MISSING_ASSET_CODE')
        missing_summary.to_excel(writer, sheet_name='缺失ASSET_CODE汇总', index=False)
        print(f"工作表 '缺失ASSET_CODE汇总' 创建完成: {len(missing_summary)} 个唯一的缺失ASSET_CODE")
        
        # 工作表2: 详细记录
        df_missing_sorted = df_missing.sort_values(['row_index', 'missing_asset_code'])
        df_missing_sorted.to_excel(writer, sheet_name='缺失ASSET_CODE详细记录', index=False)
        print(f"工作表 '缺失ASSET_CODE详细记录' 创建完成: {len(df_missing_sorted)} 条记录")
        
        # 工作表3: 受影响的完整订单行
        affected_rows = df_order[df_order.index.isin(df_missing['row_index'] - 1)].copy()
        affected_rows['ROW_NUMBER'] = affected_rows.index + 1
        
        # 添加缺失的ASSET_CODE信息
        row_missing_info = df_missing.groupby('row_index')['missing_asset_code'].apply(
            lambda x: ', '.join(sorted(x))
        ).to_dict()
        
        affected_rows['MISSING_ASSET_CODES'] = affected_rows['ROW_NUMBER'].map(row_missing_info)
        
        # 重新排列列顺序
        cols = ['ROW_NUMBER', 'ORDER_ID', 'BUYER_ID', 'BUY_QUANTITY', 'ASSET_CODE', 'MISSING_ASSET_CODES', 'PAY_TIME']
        affected_rows = affected_rows[cols]
        affected_rows = affected_rows.sort_values('ROW_NUMBER')
        
        affected_rows.to_excel(writer, sheet_name='受影响的订单行', index=False)
        print(f"工作表 '受影响的订单行' 创建完成: {len(affected_rows)} 行订单")
        
        # 工作表4: 按买家统计
        buyer_stats = df_missing.groupby('BUYER_ID').agg({
            'missing_asset_code': ['count', lambda x: ', '.join(sorted(x.unique()))],
            'ORDER_ID': 'nunique',
            'BUY_QUANTITY': 'sum'
        }).reset_index()

        # 扁平化多级列名
        buyer_stats.columns = [
            'BUYER_ID',
            'MISSING_ASSET_COUNT',
            'MISSING_ASSET_CODES',
            'AFFECTED_ORDER_COUNT',
            'TOTAL_QUANTITY'
        ]
        
        buyer_stats = buyer_stats.sort_values('MISSING_ASSET_COUNT', ascending=False)
        buyer_stats.to_excel(writer, sheet_name='按买家统计', index=False)
        print(f"工作表 '按买家统计' 创建完成: {len(buyer_stats)} 个买家")
        
        # 工作表5: 按订单统计
        order_stats = df_missing.groupby('ORDER_ID').agg({
            'missing_asset_code': ['count', lambda x: ', '.join(sorted(x.unique()))],
            'BUYER_ID': 'first',
            'BUY_QUANTITY': 'first',
            'PAY_TIME': 'first'
        }).reset_index()

        # 扁平化多级列名
        order_stats.columns = [
            'ORDER_ID',
            'MISSING_ASSET_COUNT',
            'MISSING_ASSET_CODES',
            'BUYER_ID',
            'BUY_QUANTITY',
            'PAY_TIME'
        ]
        
        order_stats = order_stats.sort_values('MISSING_ASSET_COUNT', ascending=False)
        order_stats.to_excel(writer, sheet_name='按订单统计', index=False)
        print(f"工作表 '按订单统计' 创建完成: {len(order_stats)} 个订单")
    
    print(f"\n✅ Excel文件创建成功: {excel_filename}")
    print("\n📊 文件包含以下工作表:")
    print("1. 缺失ASSET_CODE汇总 - 每个缺失ASSET_CODE的统计信息")
    print("2. 缺失ASSET_CODE详细记录 - 所有缺失记录的详细信息")
    print("3. 受影响的订单行 - 包含缺失ASSET_CODE的完整订单信息")
    print("4. 按买家统计 - 每个买家的缺失ASSET_CODE统计")
    print("5. 按订单统计 - 每个订单的缺失ASSET_CODE统计")
    
    return excel_filename

if __name__ == "__main__":
    create_missing_asset_excel()
