-- =====================================================
-- 执行所有批次插入的主脚本
-- 使用方法: mysql -u username -p database_name < execute_all_batches.sql
-- =====================================================

-- 显示开始时间
SELECT NOW() AS '开始时间', '开始执行批量插入' AS '状态';

-- 执行批次 1
SOURCE dig_asset_pay_order_batch_01.sql;

-- 执行批次 2
SOURCE dig_asset_pay_order_batch_02.sql;

-- 执行批次 3
SOURCE dig_asset_pay_order_batch_03.sql;

-- 执行批次 4
SOURCE dig_asset_pay_order_batch_04.sql;


-- 显示最终统计结果
SELECT NOW() AS '完成时间', '批量插入完成' AS '状态';

SELECT 
    COUNT(*) AS '总记录数',
    MIN(entry_time) AS '最早交易时间',
    MAX(entry_time) AS '最晚交易时间',
    SUM(CASE WHEN account_type = '在线支付' THEN income_amount ELSE 0 END) AS '总收入',
    SUM(CASE WHEN account_type = '收费' THEN expense_amount ELSE 0 END) AS '总支出费用',
    SUM(service_fee) AS '总服务费'
FROM dig_asset_pay_order;

-- 按账务类型统计
SELECT 
    account_type AS '账务类型',
    COUNT(*) AS '交易笔数',
    ROUND(SUM(income_amount), 2) AS '收入金额',
    ROUND(SUM(expense_amount), 2) AS '支出金额'
FROM dig_asset_pay_order 
GROUP BY account_type;
