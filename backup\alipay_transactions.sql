-- 支付宝账务组合查询表
-- 根据 2088260786994890-20250801-168426223-账务组合查询-simple.xls 生成

-- 创建数据库（如果需要）
-- CREATE DATABASE IF NOT EXISTS alipay_finance DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
-- USE alipay_finance;

-- 删除表（如果存在）
DROP TABLE IF EXISTS alipay_transactions;

-- 创建支付宝交易表
CREATE TABLE dig_asset_pay_order (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    serial_number INT NOT NULL COMMENT '序号',
    entry_time DATETIME NOT NULL COMMENT '入账时间',
    alipay_trade_no VARCHAR(50) NOT NULL COMMENT '支付宝交易号',
    alipay_serial_no VARCHAR(50) NOT NULL COMMENT '支付宝流水号',
    merchant_order_no VARCHAR(50) NOT NULL COMMENT '商户订单号',
    account_type VARCHAR(20) NOT NULL COMMENT '账务类型',
    income_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '收入（+元）',
    expense_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '支出（-元）',
    account_balance DECIMAL(10,2) NOT NULL COMMENT '账户余额（元）',
    service_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '服务费（元）',
    payment_channel VARCHAR(50) COMMENT '支付渠道',
    contract_product VARCHAR(50) COMMENT '签约产品',
    counterpart_account VARCHAR(100) COMMENT '对方账户',
    counterpart_name VARCHAR(100) COMMENT '对方名称',
    bank_order_no VARCHAR(50) COMMENT '银行订单号',
    product_name VARCHAR(100) COMMENT '商品名称',
    remark TEXT COMMENT '备注',
    business_base_order_no VARCHAR(50) COMMENT '业务基础订单号',
    business_order_no VARCHAR(100) COMMENT '业务订单号',
    business_bill_source VARCHAR(50) COMMENT '业务账单来源',
    business_description VARCHAR(200) COMMENT '业务描述',
    payment_remark TEXT COMMENT '付款备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_entry_time (entry_time),
    INDEX idx_alipay_trade_no (alipay_trade_no),
    INDEX idx_merchant_order_no (merchant_order_no),
    INDEX idx_account_type (account_type),
    INDEX idx_counterpart_account (counterpart_account)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付宝账务交易记录表';

-- 插入示例数据（基于Excel文件中的前几条记录）
INSERT INTO alipay_transactions (
    serial_number, entry_time, alipay_trade_no, alipay_serial_no, merchant_order_no,
    account_type, income_amount, expense_amount, account_balance, service_fee,
    payment_channel, contract_product, counterpart_account, counterpart_name,
    bank_order_no, product_name, remark, business_base_order_no, business_order_no,
    business_bill_source, business_description, payment_remark
) VALUES 
(1, '2025-07-31 21:35:54', '2025073122001480901401230459', '1470667033490724900', 'DIG_103020250731FBF08',
 '收费', 0.00, 4.19, 3935.20, 0.00,
 '支付宝账号', '即时到帐', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '', '敬请期待', '支付宝服务费[2025073122001480901401230459]', 'DIG_103020250731FBF08', 'M{DIG_103020250731FBF08}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(2, '2025-07-31 21:35:53', '2025073122001480901401230459', '1470665280583680901', 'DIG_103020250731FBF08',
 '在线支付', 699.00, 0.00, 3939.39, 4.19,
 '支付宝账号', '即时到帐', '198******45', '**程',
 '', '敬请期待', '', '', '',
 '', '', ''),

(3, '2025-07-31 20:40:25', '2025073122001427601420980248', '1470663375081836600', 'DIG_12226202507319FCC7',
 '收费', 0.00, 1.26, 3240.39, 0.00,
 '快捷支付-信用卡', '即时到帐', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '2025073170283516030560090211302', '敬请期待', '服务费[2025073122001427601420980248]', 'DIG_12226202507319FCC7', 'M{DIG_12226202507319FCC7}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(4, '2025-07-31 20:40:24', '2025073122001427601420980248', '1470665289985427601', 'DIG_12226202507319FCC7',
 '在线支付', 209.70, 0.00, 3241.65, 1.26,
 '快捷支付-信用卡', '即时到帐', '139******94', '*立',
 '2025073170283516030560090211302', '敬请期待', '', '', '',
 '', '', ''),

(5, '2025-07-31 19:49:40', '2025073122001410771404347104', '1470655205597169770', 'DIG_6396220250731C9C17',
 '收费', 0.00, 0.42, 3031.95, 0.00,
 '快捷支付-借记卡', '即时到帐', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '2025073162848989250577090200607', '敬请期待', '服务费[2025073122001410771404347104]', 'DIG_6396220250731C9C17', 'M{DIG_6396220250731C9C17}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(6, '2025-07-31 19:49:40', '2025073122001410771404347104', '1470659133758210771', 'DIG_6396220250731C9C17',
 '在线支付', 69.90, 0.00, 3032.37, 0.42,
 '快捷支付-借记卡', '即时到帐', '155******87', '**龙',
 '2025073162848989250577090200607', '敬请期待', '', '', '',
 '', '', ''),

(7, '2025-07-31 19:24:59', '2025073123001495271452648820', '1470654546004593270', 'DIG_103672202507314B4A9',
 '收费', 0.00, 0.01, 2962.47, 0.00,
 '快捷支付-借记卡', '即时到帐', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '0731a67525552720', '数资萌使·凌宝', '服务费[2025073123001495271452648820]', 'DIG_103672202507314B4A9', 'M{DIG_103672202507314B4A9}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(8, '2025-07-31 19:24:58', '2025073123001495271452648820', '1470657435737795271', 'DIG_103672202507314B4A9',
 '在线支付', 1.00, 0.00, 2962.48, 0.01,
 '快捷支付-借记卡', '即时到帐', '176***@qq.com', '*龙',
 '0731a67525552720', '数资萌使·凌宝', '', '', '',
 '', '', ''),

(9, '2025-07-31 19:24:35', '2025073122001456181431170946', '1470687738401320180', 'DIG_10364920250731B04C0',
 '收费', 0.00, 0.01, 2961.48, 0.00,
 '快捷支付-借记卡', '', '<EMAIL>', '支付宝（中国）网络技术有限公司',
 '2025073162602595660518090111408', '数资萌使·凌宝', '服务费[2025073122001456181431170946]', 'DIG_10364920250731B04C0', 'M{DIG_10364920250731B04C0}',
 '蚂蚁商家中心', '0030038|软件服务费-支付宝服务费', ''),

(10, '2025-07-31 19:24:35', '2025073122001456181431170946', '1470692381733056181', 'DIG_10364920250731B04C0',
 '在线支付', 1.00, 0.00, 2961.49, 0.01,
 '快捷支付-借记卡', '即时到帐', '392***@qq.com', '*坡',
 '2025073162602595660518090111408', '数资萌使·凌宝', '', '', '',
 '', '', '');

-- 查询验证数据
SELECT 
    serial_number AS '序号',
    entry_time AS '入账时间',
    alipay_trade_no AS '支付宝交易号',
    merchant_order_no AS '商户订单号',
    account_type AS '账务类型',
    income_amount AS '收入（元）',
    expense_amount AS '支出（元）',
    account_balance AS '账户余额（元）',
    payment_channel AS '支付渠道',
    counterpart_name AS '对方名称',
    product_name AS '商品名称'
FROM alipay_transactions 
ORDER BY serial_number;

-- 统计查询示例
-- 按账务类型统计
SELECT 
    account_type AS '账务类型',
    COUNT(*) AS '交易笔数',
    SUM(income_amount) AS '总收入',
    SUM(expense_amount) AS '总支出',
    SUM(service_fee) AS '总服务费'
FROM alipay_transactions 
GROUP BY account_type;

-- 按支付渠道统计
SELECT 
    payment_channel AS '支付渠道',
    COUNT(*) AS '交易笔数',
    SUM(income_amount) AS '总收入'
FROM alipay_transactions 
WHERE account_type = '在线支付'
GROUP BY payment_channel;

-- 按日期统计
SELECT 
    DATE(entry_time) AS '日期',
    COUNT(*) AS '交易笔数',
    SUM(income_amount) AS '当日收入',
    SUM(expense_amount) AS '当日支出'
FROM alipay_transactions 
GROUP BY DATE(entry_time)
ORDER BY DATE(entry_time);
