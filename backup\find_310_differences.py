import pandas as pd
from datetime import datetime

def comprehensive_difference_analysis():
    """
    全面分析可能的310个差异的来源
    """
    print("全面分析310个差异的可能来源...")
    print("=" * 60)
    
    # 1. 读取所有文件
    print("1. 读取所有相关文件...")
    
    # dig_asset_order.xlsx (原始文件)
    df_order_1 = pd.read_excel('dig_asset_order.xlsx')
    order_1_codes = set()
    for asset_code in df_order_1['ASSET_CODE']:
        if not pd.isna(asset_code):
            codes = [code.strip() for code in str(asset_code).split(',')]
            codes = [code for code in codes if code]
            order_1_codes.update(codes)
    
    # dig_asset_order_2.xlsx (第二个文件)
    df_order_2 = pd.read_excel('dig_asset_order_2.xlsx')
    order_2_codes = set()
    order_2_all_entries = []
    
    for index, row in df_order_2.iterrows():
        asset_code = row['ASSET_CODE']
        if not pd.isna(asset_code):
            codes = [code.strip() for code in str(asset_code).split(',')]
            codes = [code for code in codes if code]
            order_2_codes.update(codes)
            
            # 保存所有条目
            for code in codes:
                order_2_all_entries.append(code)
    
    # dig_data_asset_2.xlsx (资产文件)
    df_asset = pd.read_excel('dig_data_asset_2.xlsx')
    asset_codes = set()
    for asset_code in df_asset['ASSET_CODE']:
        if not pd.isna(asset_code):
            codes = [code.strip() for code in str(asset_code).split(',')]
            codes = [code for code in codes if code]
            asset_codes.update(codes)
    
    print(f"   dig_asset_order.xlsx: {len(order_1_codes)} 个唯一ASSET_CODE")
    print(f"   dig_asset_order_2.xlsx: {len(order_2_codes)} 个唯一ASSET_CODE")
    print(f"   dig_asset_order_2.xlsx: {len(order_2_all_entries)} 个总条目（包含重复）")
    print(f"   dig_data_asset_2.xlsx: {len(asset_codes)} 个唯一ASSET_CODE")
    
    # 2. 尝试不同的差异计算方法
    print(f"\n2. 尝试不同的差异计算方法...")
    
    # 方法1: 基于dig_asset_order_2.xlsx的唯一ASSET_CODE
    diff_1 = order_2_codes - asset_codes
    print(f"   方法1 - 基于唯一ASSET_CODE: {len(diff_1)} 个差异")
    
    # 方法2: 合并两个订单文件的ASSET_CODE
    combined_order_codes = order_1_codes | order_2_codes
    diff_2 = combined_order_codes - asset_codes
    print(f"   方法2 - 合并两个订单文件: {len(diff_2)} 个差异")
    
    # 方法3: 基于完整的数字范围
    # 找出所有ASSET_CODE的数字范围
    all_numbers = []
    for codes in [order_1_codes, order_2_codes, asset_codes]:
        for code in codes:
            if '#' in code:
                try:
                    num = int(code.split('#')[-1])
                    all_numbers.append(num)
                except:
                    pass
    
    if all_numbers:
        min_num = min(all_numbers)
        max_num = max(all_numbers)
        print(f"   数字范围: {min_num} - {max_num} (总共 {max_num - min_num + 1} 个)")
        
        # 生成完整范围的ASSET_CODE
        full_range_codes = set()
        for i in range(min_num, max_num + 1):
            full_range_codes.add(f"LY#25#{i:04d}")
        
        diff_3 = full_range_codes - asset_codes
        print(f"   方法3 - 基于完整数字范围: {len(diff_3)} 个差异")
        
        # 方法4: 基于订单文件中出现的范围
        order_numbers = []
        for codes in [order_1_codes, order_2_codes]:
            for code in codes:
                if '#' in code:
                    try:
                        num = int(code.split('#')[-1])
                        order_numbers.append(num)
                    except:
                        pass
        
        if order_numbers:
            order_min = min(order_numbers)
            order_max = max(order_numbers)
            order_range_codes = set()
            for i in range(order_min, order_max + 1):
                order_range_codes.add(f"LY#25#{i:04d}")
            
            diff_4 = order_range_codes - asset_codes
            print(f"   方法4 - 基于订单范围({order_min}-{order_max}): {len(diff_4)} 个差异")
    
    # 3. 找出最接近310的方法
    differences = {
        '方法1_唯一ASSET_CODE': diff_1,
        '方法2_合并订单文件': diff_2,
        '方法3_完整数字范围': diff_3 if 'diff_3' in locals() else set(),
        '方法4_订单数字范围': diff_4 if 'diff_4' in locals() else set()
    }
    
    closest_to_310 = None
    closest_diff = float('inf')
    
    for method, diff_set in differences.items():
        diff_count = len(diff_set)
        distance = abs(diff_count - 310)
        print(f"   {method}: {diff_count} 个差异 (距离310: {distance})")
        
        if distance < closest_diff:
            closest_diff = distance
            closest_to_310 = (method, diff_set, diff_count)
    
    # 4. 导出最接近310的结果
    if closest_to_310:
        method_name, diff_set, diff_count = closest_to_310
        print(f"\n3. 导出最接近310的结果: {method_name} ({diff_count}个)")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_filename = f"closest_to_310_differences_{diff_count}_{timestamp}.xlsx"
        
        # 创建详细的差异列表
        diff_list = sorted(list(diff_set))
        
        # 为每个差异ASSET_CODE查找相关信息
        detailed_data = []
        for code in diff_list:
            # 检查是否在订单文件中出现
            in_order_1 = code in order_1_codes
            in_order_2 = code in order_2_codes
            
            detailed_data.append({
                'MISSING_ASSET_CODE': code,
                'IN_ORDER_1': in_order_1,
                'IN_ORDER_2': in_order_2,
                'METHOD': method_name,
                'NUMBER': int(code.split('#')[-1]) if '#' in code else None
            })
        
        # 创建Excel文件
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            
            # 工作表1: 差异ASSET_CODE列表
            df_diff = pd.DataFrame(detailed_data)
            df_diff.to_excel(writer, sheet_name='差异ASSET_CODE列表', index=False)
            
            # 工作表2: 统计信息
            stats_data = [
                ['计算方法', method_name],
                ['差异ASSET_CODE数量', diff_count],
                ['距离310的差距', abs(diff_count - 310)],
                ['', ''],
                ['数据源统计', ''],
                ['dig_asset_order.xlsx唯一ASSET_CODE', len(order_1_codes)],
                ['dig_asset_order_2.xlsx唯一ASSET_CODE', len(order_2_codes)],
                ['dig_asset_order_2.xlsx总条目', len(order_2_all_entries)],
                ['dig_data_asset_2.xlsx唯一ASSET_CODE', len(asset_codes)],
                ['', ''],
                ['范围信息', ''],
                ['最小数字', min(all_numbers) if all_numbers else 'N/A'],
                ['最大数字', max(all_numbers) if all_numbers else 'N/A'],
                ['总范围', max(all_numbers) - min(all_numbers) + 1 if all_numbers else 'N/A']
            ]
            
            df_stats = pd.DataFrame(stats_data, columns=['统计项目', '数值'])
            df_stats.to_excel(writer, sheet_name='统计信息', index=False)
        
        print(f"   Excel文件已创建: {excel_filename}")
        
        # 显示前10个差异
        print(f"\n前10个差异ASSET_CODE:")
        for i, code in enumerate(diff_list[:10], 1):
            print(f"   {i:2d}. {code}")
        
        return excel_filename, diff_count
    
    return None, 0

if __name__ == "__main__":
    excel_file, count = comprehensive_difference_analysis()
