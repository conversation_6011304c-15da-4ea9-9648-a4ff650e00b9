import pandas as pd
import glob

def verify_excel_content():
    """
    验证生成的Excel文件内容
    """
    # 找到最新的Excel文件
    excel_files = glob.glob('missing_asset_codes_*.xlsx')
    if not excel_files:
        print("没有找到Excel文件")
        return
    
    latest_file = max(excel_files)
    print(f"验证文件: {latest_file}")
    
    # 读取所有工作表
    excel_file = pd.ExcelFile(latest_file)
    print(f"\n工作表列表: {excel_file.sheet_names}")
    
    for sheet_name in excel_file.sheet_names:
        print(f"\n{'='*50}")
        print(f"工作表: {sheet_name}")
        print(f"{'='*50}")
        
        df = pd.read_excel(latest_file, sheet_name=sheet_name)
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        if len(df) > 0:
            print(f"\n前3行数据:")
            for i in range(min(3, len(df))):
                print(f"第{i+1}行:")
                for col in df.columns:
                    value = df.iloc[i][col]
                    if pd.isna(value):
                        value = 'NULL'
                    print(f"  {col}: {value}")
                print()

if __name__ == "__main__":
    verify_excel_content()
