import pandas as pd

def count_asset_codes_in_file(filename):
    """
    统计指定Excel文件中的ASSET_CODE数量
    """
    print(f"正在分析文件: {filename}")
    print("=" * 50)
    
    try:
        # 读取Excel文件
        df = pd.read_excel(filename)
        print(f"文件读取成功")
        print(f"总行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查是否有ASSET_CODE列
        if 'ASSET_CODE' not in df.columns:
            print("错误: 文件中没有找到ASSET_CODE列")
            return None
        
        # 统计ASSET_CODE
        asset_codes = set()
        asset_code_details = []
        
        print(f"\n开始处理ASSET_CODE...")
        
        for index, row in df.iterrows():
            asset_code = row['ASSET_CODE']
            if not pd.isna(asset_code):
                # 转换为字符串并处理逗号分隔的情况
                asset_code_str = str(asset_code).strip()
                if asset_code_str:
                    # 按逗号分割
                    codes = [code.strip() for code in asset_code_str.split(',')]
                    codes = [code for code in codes if code]  # 过滤空字符串
                    
                    asset_codes.update(codes)
                    
                    # 保存详细信息
                    asset_code_details.append({
                        'row_index': index + 1,
                        'original_asset_code': asset_code_str,
                        'split_count': len(codes),
                        'split_codes': codes
                    })
        
        print(f"\n统计结果:")
        print(f"包含ASSET_CODE的行数: {len(asset_code_details)}")
        print(f"唯一ASSET_CODE总数: {len(asset_codes)}")
        
        # 分析ASSET_CODE的数字范围
        numbers = []
        for code in asset_codes:
            if '#' in code:
                try:
                    num = int(code.split('#')[-1])
                    numbers.append(num)
                except:
                    pass
        
        if numbers:
            print(f"ASSET_CODE数字范围: {min(numbers)} - {max(numbers)}")
        
        # 显示前10个ASSET_CODE
        print(f"\n前10个ASSET_CODE:")
        for i, code in enumerate(sorted(asset_codes)[:10], 1):
            print(f"  {i:2d}. {code}")
        
        # 检查是否有逗号分隔的情况
        comma_separated_rows = [detail for detail in asset_code_details if detail['split_count'] > 1]
        if comma_separated_rows:
            print(f"\n包含逗号分隔ASSET_CODE的行数: {len(comma_separated_rows)}")
            total_split_codes = sum(detail['split_count'] for detail in comma_separated_rows)
            print(f"逗号分隔产生的额外ASSET_CODE数量: {total_split_codes - len(comma_separated_rows)}")
            print(f"前5个逗号分隔的例子:")
            for i, detail in enumerate(comma_separated_rows[:5], 1):
                print(f"  {i}. 行{detail['row_index']}: {detail['original_asset_code']}")
                print(f"     拆分为 {detail['split_count']} 个: {detail['split_codes']}")
        else:
            print(f"\n没有发现逗号分隔的ASSET_CODE")
        
        return {
            'total_rows': len(df),
            'rows_with_asset_code': len(asset_code_details),
            'unique_asset_codes': len(asset_codes),
            'asset_codes': asset_codes,
            'comma_separated_rows': len(comma_separated_rows) if comma_separated_rows else 0,
            'number_range': (min(numbers), max(numbers)) if numbers else None
        }
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def main():
    """
    主函数：统计所有相关Excel文件中的ASSET_CODE
    """
    files_to_check = [
        'dig_asset_order.xlsx',
        'dig_asset_order_2.xlsx', 
        'dig_data_asset_2.xlsx'
    ]
    
    results = {}
    
    for filename in files_to_check:
        print(f"\n{'='*60}")
        result = count_asset_codes_in_file(filename)
        if result:
            results[filename] = result
        print()
    
    # 汇总比较
    if len(results) > 1:
        print(f"\n{'='*60}")
        print("汇总比较:")
        print("=" * 60)
        
        for filename, result in results.items():
            print(f"{filename}:")
            print(f"  总行数: {result['total_rows']}")
            print(f"  包含ASSET_CODE的行数: {result['rows_with_asset_code']}")
            print(f"  唯一ASSET_CODE数量: {result['unique_asset_codes']}")
            print(f"  逗号分隔行数: {result['comma_separated_rows']}")
            if result['number_range']:
                print(f"  数字范围: {result['number_range'][0]} - {result['number_range'][1]}")
            print()

if __name__ == "__main__":
    main()
