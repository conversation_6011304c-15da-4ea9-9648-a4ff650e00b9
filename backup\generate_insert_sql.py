import pandas as pd
from datetime import datetime

def format_value_for_insert(value, column_name):
    """格式化值用于INSERT语句"""
    if pd.isna(value) or value is None:
        return 'NULL'
    
    # 字符串类型字段需要加引号
    string_fields = ['ACQUIRE_TYPE', 'ASSET_NAME', 'ASSET_CODE', 'CHAIN_ID', 
                    'CHAIN_HASH', 'STATUS_CD']
    
    if column_name in string_fields:
        # 转义单引号
        escaped_value = str(value).replace("'", "''")
        return f"'{escaped_value}'"
    
    # 日期时间字段
    datetime_fields = ['CHAIN_TIME', 'STATUS_DATE', 'CREATE_DATE', 'UPDATE_DATE']
    if column_name in datetime_fields:
        if isinstance(value, str):
            return f"'{value}'"
        elif hasattr(value, 'strftime'):
            return f"'{value.strftime('%Y-%m-%d %H:%M:%S')}'"
        else:
            return f"'{str(value)}'"
    
    # 数值类型直接返回
    return str(value)

def generate_insert_statements():
    """生成INSERT语句"""
    # 读取Excel文件
    df = pd.read_excel('dig_asset_order.xlsx', sheet_name='update_dig_data_asset')
    
    insert_statements = []
    
    # 获取所有列名（排除AUTO_INCREMENT的主键）
    columns = [col for col in df.columns if col != 'DATA_ASSET_ID']
    
    for index, row in df.iterrows():
        # 构建VALUES子句
        values = []
        for column in columns:
            value = format_value_for_insert(row[column], column)
            values.append(value)
        
        # 生成INSERT语句
        columns_str = ', '.join([f'`{col}`' for col in columns])
        values_str = ', '.join(values)
        
        insert_sql = f"""INSERT INTO `dig_data_asset` ({columns_str}) 
VALUES ({values_str});"""
        
        insert_statements.append(insert_sql)
    
    return insert_statements

# 生成INSERT语句
statements = generate_insert_statements()

# 输出到文件
with open('insert_dig_data_asset.sql', 'w', encoding='utf-8') as f:
    f.write("-- 用户数据资产表插入语句\n")
    f.write(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    f.write(f"-- 总计: {len(statements)} 条插入语句\n\n")
    
    for i, statement in enumerate(statements, 1):
        f.write(f"-- 第 {i} 条插入语句\n")
        f.write(statement)
        f.write("\n\n")

print(f"已生成 {len(statements)} 条INSERT语句，保存到 insert_dig_data_asset.sql 文件中")
print("\n前3条语句预览:")
for i, statement in enumerate(statements[:3], 1):
    print(f"\n第 {i} 条:")
    print(statement)
