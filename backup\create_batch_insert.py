#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def create_batch_insert_files():
    """将大量INSERT语句分批创建多个文件"""
    
    # 读取原始INSERT语句文件
    with open('insert_statements.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取所有INSERT语句
    insert_statements = re.findall(r'INSERT INTO dig_asset_pay_order.*?;', content, re.DOTALL)
    
    print(f"找到 {len(insert_statements)} 条INSERT语句")
    
    # 分批处理，每批1000条
    batch_size = 1000
    total_batches = (len(insert_statements) + batch_size - 1) // batch_size
    
    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, len(insert_statements))
        batch_statements = insert_statements[start_idx:end_idx]
        
        # 创建批次文件
        filename = f'dig_asset_pay_order_batch_{batch_num + 1:02d}.sql'
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"""-- =====================================================
-- 支付宝账务数据批量插入脚本 - 批次 {batch_num + 1}/{total_batches}
-- 表名: dig_asset_pay_order
-- 记录范围: {start_idx + 1} - {end_idx}
-- 记录数量: {len(batch_statements)}
-- =====================================================

-- 设置会话参数以优化批量插入性能
SET autocommit = 0;
SET unique_checks = 0;
SET foreign_key_checks = 0;
SET sql_log_bin = 0;

-- 开始事务
START TRANSACTION;

""")
            
            # 写入INSERT语句
            for stmt in batch_statements:
                f.write(stmt + "\n\n")
            
            f.write("""
-- 提交事务
COMMIT;

-- 恢复会话参数
SET unique_checks = 1;
SET foreign_key_checks = 1;
SET autocommit = 1;
SET sql_log_bin = 1;

-- 显示当前批次插入结果
SELECT 
    COUNT(*) AS '当前总记录数',
    MAX(serial_number) AS '最大序号'
FROM dig_asset_pay_order;
""")
        
        print(f"创建批次文件: {filename} (记录 {start_idx + 1}-{end_idx})")
    
    # 创建执行所有批次的主脚本
    with open('execute_all_batches.sql', 'w', encoding='utf-8') as f:
        f.write("""-- =====================================================
-- 执行所有批次插入的主脚本
-- 使用方法: mysql -u username -p database_name < execute_all_batches.sql
-- =====================================================

-- 显示开始时间
SELECT NOW() AS '开始时间', '开始执行批量插入' AS '状态';

""")
        
        for batch_num in range(total_batches):
            filename = f'dig_asset_pay_order_batch_{batch_num + 1:02d}.sql'
            f.write(f"-- 执行批次 {batch_num + 1}\n")
            f.write(f"SOURCE {filename};\n\n")
        
        f.write("""
-- 显示最终统计结果
SELECT NOW() AS '完成时间', '批量插入完成' AS '状态';

SELECT 
    COUNT(*) AS '总记录数',
    MIN(entry_time) AS '最早交易时间',
    MAX(entry_time) AS '最晚交易时间',
    SUM(CASE WHEN account_type = '在线支付' THEN income_amount ELSE 0 END) AS '总收入',
    SUM(CASE WHEN account_type = '收费' THEN expense_amount ELSE 0 END) AS '总支出费用',
    SUM(service_fee) AS '总服务费'
FROM dig_asset_pay_order;

-- 按账务类型统计
SELECT 
    account_type AS '账务类型',
    COUNT(*) AS '交易笔数',
    ROUND(SUM(income_amount), 2) AS '收入金额',
    ROUND(SUM(expense_amount), 2) AS '支出金额'
FROM dig_asset_pay_order 
GROUP BY account_type;
""")
    
    print(f"\n批次文件创建完成:")
    print(f"- 总批次数: {total_batches}")
    print(f"- 每批记录数: {batch_size}")
    print(f"- 主执行脚本: execute_all_batches.sql")
    print(f"\n执行方法:")
    print(f"1. 单独执行: mysql -u username -p database_name < dig_asset_pay_order_batch_01.sql")
    print(f"2. 批量执行: mysql -u username -p database_name < execute_all_batches.sql")

if __name__ == "__main__":
    create_batch_insert_files()
